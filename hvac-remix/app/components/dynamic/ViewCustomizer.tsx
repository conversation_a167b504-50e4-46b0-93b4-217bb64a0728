import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Label } from "~/components/ui/label";
import { GripVertical, Eye, EyeOff } from "lucide-react";

interface ViewCustomizerProps {
  entityType: string;
  availableFields: Array<{ id: string; name: string; label: string }>;
  viewDefinitions: ViewDefinition[];
  currentView?: ViewDefinition;
  onViewChange: (viewId: string) => void;
  onSaveView: (layout: any) => void;
  className?: string;
}

// Sortable item component
function SortableField({ field, isVisible, onToggleVisibility }) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: field.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center justify-between p-3 bg-white border rounded-md mb-2"
    >
      <div className="flex items-center">
        <button
          type="button"
          className="cursor-grab p-1 mr-2 text-gray-400 hover:text-gray-600"
          {...attributes}
          {...listeners}
        >
          <GripVertical className="h-4 w-4" />
        </button>
        <span>{field.label}</span>
      </div>

      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={() => onToggleVisibility(field.id)}
      >
        {isVisible ? (
          <Eye className="h-4 w-4" />
        ) : (
          <EyeOff className="h-4 w-4 text-gray-400" />
        )}
      </Button>
    </div>
  );
}

export function ViewCustomizer({
  entityType,
  availableFields,
  viewDefinitions,
  currentView,
  onViewChange,
  onSaveView,
  className = ""
}: ViewCustomizerProps) {
  // Parse current view layout
  const initialLayout = currentView?.layout ? JSON.parse(currentView.layout) : {
    visibleFields: availableFields.map(f => f.id),
    order: availableFields.map(f => f.id)
  };

  const [layout, setLayout] = useState(initialLayout);
  const [isCustomizing, setIsCustomizing] = useState(false);

  // Handle field reordering
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setLayout(prev => {
        const oldIndex = prev.order.indexOf(active.id);
        const newIndex = prev.order.indexOf(over.id);

        const newOrder = [...prev.order];
        newOrder.splice(oldIndex, 1);
        newOrder.splice(newIndex, 0, active.id);

        return {
          ...prev,
          order: newOrder
        };
      });
    }
  };

  // Toggle field visibility
  const toggleFieldVisibility = (fieldId) => {
    setLayout(prev => {
      const visibleFields = [...prev.visibleFields];

      if (visibleFields.includes(fieldId)) {
        return {
          ...prev,
          visibleFields: visibleFields.filter(id => id !== fieldId)
        };
      } else {
        return {
          ...prev,
          visibleFields: [...visibleFields, fieldId]
        };
      }
    });
  };

  // Save the current layout
  const handleSave = () => {
    onSaveView(layout);
    setIsCustomizing(false);
  };

  // Get fields in the correct order
  const orderedFields = layout.order
    .map(id => availableFields.find(f => f.id === id))
    .filter(Boolean);

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>View Customization</CardTitle>
        </div>

        <div className="flex items-center space-x-2">
          <Select
            value={currentView?.id || ""}
            onValueChange={onViewChange}
            disabled={isCustomizing}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select a view" />
            </SelectTrigger>
            <SelectContent>
              {viewDefinitions.map(view => (
                <SelectItem key={view.id} value={view.id}>
                  {view.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {isCustomizing ? (
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setIsCustomizing(false)}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                Save
              </Button>
            </div>
          ) : (
            <Button onClick={() => setIsCustomizing(true)}>
              Customize
            </Button>
          )}
        </div>
      </CardHeader>

      {isCustomizing && (
        <CardContent>
          <div className="mb-4">
            <Label>Drag to reorder fields or toggle visibility</Label>
          </div>

          <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
            <SortableContext items={layout.order} strategy={verticalListSortingStrategy}>
              {orderedFields.map(field => (
                <SortableField
                  key={field.id}
                  field={field}
                  isVisible={layout.visibleFields.includes(field.id)}
                  onToggleVisibility={toggleFieldVisibility}
                />
              ))}
            </SortableContext>
          </DndContext>
        </CardContent>
      )}
    </Card>
  );
}